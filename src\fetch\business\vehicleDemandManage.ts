import { request } from '@/fetch/core';
import { Method } from '@/fetch/core/constant';

const provinceCityCountryList = {
  code: '0000',
  data: [
    {
      id: 1,
      name: '北京',
      children: [
        {
          id: 11,
          name: '北京市',
          children: [
            { id: 111, name: '东城区', children: [] },
            { id: 112, name: '西城区', children: [] },
            { id: 113, name: '朝阳区', children: [] },
            { id: 114, name: '丰台区', children: [] },
            { id: 115, name: '石景山区', children: [] },
            { id: 116, name: '海淀区', children: [] },
            { id: 117, name: '门头沟区', children: [] },
            { id: 118, name: '房山区', children: [] },
            { id: 119, name: '通州区', children: [] },
            { id: 120, name: '顺义区', children: [] },
            { id: 121, name: '昌平区', children: [] },
            { id: 122, name: '大兴区', children: [] },
            { id: 123, name: '怀柔区', children: [] },
            { id: 124, name: '平谷区', children: [] },
            { id: 125, name: '密云区', children: [] },
            { id: 126, name: '延庆区', children: [] },
          ],
        },
      ],
    },
    {
      id: 2,
      name: '上海',
      children: [
        {
          id: 21,
          name: '上海市',
          children: [
            { id: 211, name: '黄浦区', children: [] },
            { id: 212, name: '徐汇区', children: [] },
            { id: 213, name: '长宁区', children: [] },
            { id: 214, name: '静安区', children: [] },
            { id: 215, name: '普陀区', children: [] },
            { id: 216, name: '虹口区', children: [] },
            { id: 217, name: '杨浦区', children: [] },
            { id: 218, name: '闵行区', children: [] },
            { id: 219, name: '宝山区', children: [] },
            { id: 220, name: '嘉定区', children: [] },
            { id: 221, name: '浦东新区', children: [] },
            { id: 222, name: '金山区', children: [] },
            { id: 223, name: '松江区', children: [] },
            { id: 224, name: '青浦区', children: [] },
            { id: 225, name: '奉贤区', children: [] },
            { id: 226, name: '崇明区', children: [] },
          ],
        },
      ],
    },
    {
      id: 3,
      name: '广东',
      children: [
        {
          id: 31,
          name: '广州市',
          children: [
            { id: 311, name: '荔湾区', children: [] },
            { id: 312, name: '越秀区', children: [] },
            { id: 313, name: '海珠区', children: [] },
            { id: 314, name: '天河区', children: [] },
            { id: 315, name: '白云区', children: [] },
            { id: 316, name: '黄埔区', children: [] },
            { id: 317, name: '番禺区', children: [] },
            { id: 318, name: '花都区', children: [] },
            { id: 319, name: '南沙区', children: [] },
            { id: 320, name: '从化区', children: [] },
            { id: 321, name: '增城区', children: [] },
          ],
        },
        {
          id: 32,
          name: '深圳市',
          children: [
            { id: 321, name: '罗湖区', children: [] },
            { id: 322, name: '福田区', children: [] },
            { id: 323, name: '南山区', children: [] },
            { id: 324, name: '宝安区', children: [] },
            { id: 325, name: '龙岗区', children: [] },
            { id: 326, name: '盐田区', children: [] },
            { id: 327, name: '龙华区', children: [] },
            { id: 328, name: '坪山区', children: [] },
            { id: 329, name: '光明区', children: [] },
            { id: 330, name: '大鹏新区', children: [] },
          ],
        },
      ],
    },
    // 异常情况：空名称
    {
      id: 4,
      name: '',
      children: [
        {
          id: 41,
          name: '测试市',
          children: [{ id: 411, name: '测试区', children: [] }],
        },
      ],
    },
    // 异常情况：null children
    {
      id: 5,
      name: '测试省份',
      children: null,
    },
    // 异常情况：超长名称
    {
      id: 6,
      name: '这是一个非常非常非常非常非常非常非常非常非常非常长的省份名称用于测试前端显示',
      children: [
        {
          id: 61,
          name: '这是一个非常非常非常非常非常非常非常非常非常非常长的城市名称用于测试前端显示',
          children: [
            {
              id: 611,
              name: '这是一个非常非常非常非常非常非常非常非常非常非常长的区县名称用于测试前端显示',
              children: [],
            },
          ],
        },
      ],
    },
  ],
};

const provinceAgencyAreaList = {
  code: '0000',
  data: [
    {
      code: 'BJ',
      name: '北京省区',
      children: [
        {
          code: 'BJ_NORTH',
          name: '北京北区',
          children: [
            { code: 'BJ_NORTH_001', name: '朝阳片区', children: [] },
            { code: 'BJ_NORTH_002', name: '海淀片区', children: [] },
            { code: 'BJ_NORTH_003', name: '昌平片区', children: [] },
          ],
        },
        {
          code: 'BJ_SOUTH',
          name: '北京南区',
          children: [
            { code: 'BJ_SOUTH_001', name: '丰台片区', children: [] },
            { code: 'BJ_SOUTH_002', name: '大兴片区', children: [] },
            { code: 'BJ_SOUTH_003', name: '房山片区', children: [] },
          ],
        },
      ],
    },
    {
      code: 'SH',
      name: '上海省区',
      children: [
        {
          code: 'SH_EAST',
          name: '上海东区',
          children: [
            { code: 'SH_EAST_001', name: '浦东片区', children: [] },
            { code: 'SH_EAST_002', name: '杨浦片区', children: [] },
          ],
        },
        {
          code: 'SH_WEST',
          name: '上海西区',
          children: [
            { code: 'SH_WEST_001', name: '徐汇片区', children: [] },
            { code: 'SH_WEST_002', name: '长宁片区', children: [] },
          ],
        },
      ],
    },
    // 异常情况：空code
    {
      code: '',
      name: '测试省区',
      children: [
        {
          code: 'TEST_001',
          name: '测试区域',
          children: [{ code: 'TEST_001_001', name: '测试片区', children: [] }],
        },
      ],
    },
    // 异常情况：null值
    {
      code: null,
      name: null,
      children: null,
    },
    // 异常情况：特殊字符
    {
      code: 'SPECIAL_@#$%',
      name: '特殊字符省区@#$%^&*()',
      children: [
        {
          code: 'SPECIAL_@#$%_001',
          name: '特殊字符区域@#$%^&*()',
          children: [
            {
              code: 'SPECIAL_@#$%_001_001',
              name: '特殊字符片区@#$%^&*()',
              children: [],
            },
          ],
        },
      ],
    },
  ],
};

const stationList = {
  code: '0000',
  data: [
    { stationName: '北京朝阳站', stationNumber: 'BJ_CY_001' },
    { stationName: '北京海淀站', stationNumber: 'BJ_HD_002' },
    { stationName: '上海浦东站', stationNumber: 'SH_PD_001' },
    { stationName: '上海徐汇站', stationNumber: 'SH_XH_002' },
    { stationName: '深圳南山站', stationNumber: 'SZ_NS_001' },
    { stationName: '深圳宝安站', stationNumber: 'SZ_BA_002' },
    { stationName: '广州天河站', stationNumber: 'GZ_TH_001' },
    { stationName: '广州番禺站', stationNumber: 'GZ_PY_002' },
    // 异常情况：空站点名称
    { stationName: '', stationNumber: 'EMPTY_001' },
    // 异常情况：空站点编号
    { stationName: '测试站点', stationNumber: '' },
    // 异常情况：null值
    { stationName: null, stationNumber: null },
    // 异常情况：超长名称
    {
      stationName:
        '这是一个非常非常非常非常非常非常非常非常非常非常长的站点名称用于测试前端显示效果',
      stationNumber: 'VERY_LONG_STATION_NAME_FOR_TESTING_FRONTEND_DISPLAY_001',
    },
    // 异常情况：特殊字符
    {
      stationName: '特殊字符站点@#$%^&*()',
      stationNumber: 'SPECIAL_CHAR_@#$%_001',
    },
    // 异常情况：数字开头的站点编号
    { stationName: '数字开头站点', stationNumber: '123_NUMERIC_START' },
  ],
};

const vehicleModelList = {
  code: '0000',
  data: [
    { vehicleModelType: 'MODEL_A', vehicleModelName: 'A型无人配送车' },
    { vehicleModelType: 'MODEL_B', vehicleModelName: 'B型无人配送车' },
    { vehicleModelType: 'MODEL_C', vehicleModelName: 'C型无人配送车' },
    { vehicleModelType: 'MODEL_D', vehicleModelName: 'D型无人配送车' },
    { vehicleModelType: 'MODEL_E', vehicleModelName: 'E型无人配送车' },
    { vehicleModelType: 'HEAVY_DUTY', vehicleModelName: '重型配送车' },
    { vehicleModelType: 'LIGHT_DUTY', vehicleModelName: '轻型配送车' },
    { vehicleModelType: 'CAMPUS', vehicleModelName: '校园配送车' },
    { vehicleModelType: 'URBAN', vehicleModelName: '城市配送车' },
    { vehicleModelType: 'SUBURBAN', vehicleModelName: '郊区配送车' },
    // 异常情况：空车型
    { vehicleModelType: '', vehicleModelName: '空车型测试' },
    // 异常情况：空车型名称
    { vehicleModelType: 'EMPTY_NAME', vehicleModelName: '' },
    // 异常情况：null值
    { vehicleModelType: null, vehicleModelName: null },
    // 异常情况：超长名称
    {
      vehicleModelType: 'VERY_LONG_MODEL_TYPE_FOR_TESTING',
      vehicleModelName:
        '这是一个非常非常非常非常非常非常非常非常非常非常长的车型名称用于测试前端显示效果',
    },
    // 异常情况：特殊字符
    {
      vehicleModelType: 'SPECIAL_@#$%',
      vehicleModelName: '特殊字符车型@#$%^&*()',
    },
    // 异常情况：数字开头
    { vehicleModelType: '123_NUMERIC', vehicleModelName: '123数字开头车型' },
  ],
};

const vehicleDemandPage = {
  code: '0000',
  data: {
    pageNum: 1,
    pageSize: 10,
    pages: 5,
    total: 45,
    countMap: {
      TOTAL: 45,
      REVIEW: 15,
      PLACED: 20,
      REJECTED: 10,
    },
    list: [
      {
        id: 1,
        requirementNumber: 'REQ202401001',
        contryName: '北京-北京市-朝阳区',
        areaName: '北京省区-北京北区',
        stationName: '北京朝阳站',
        stationNumber: 'BJ_CY_001',
        address: '北京市朝阳区建国路88号',
        stationUseCaseName: '快递配送',
        vehicleModelName: 'A型无人配送车',
        count: 5,
        contact: '张三',
        contactPhone: '13800138001',
        alternateContact: '李四',
        alternateContactPhone: '13800138002',
        createTime: '2024-01-15 10:30:00',
        status: 'REVIEW',
        statusName: '审批中',
        reviewerUser: '王五',
        statusModifyTime: '2024-01-15 14:20:00',
      },
      {
        id: 2,
        requirementNumber: 'REQ202401002',
        contryName: '上海-上海市-浦东新区',
        areaName: '上海省区-上海东区',
        stationName: '上海浦东站',
        stationNumber: 'SH_PD_001',
        address: '上海市浦东新区陆家嘴环路1000号',
        stationUseCaseName: '校园配送',
        vehicleModelName: 'B型无人配送车',
        count: 3,
        contact: '赵六',
        contactPhone: '13800138003',
        alternateContact: '钱七',
        alternateContactPhone: '13800138004',
        createTime: '2024-01-16 09:15:00',
        status: 'PLACED',
        statusName: '已下单',
        reviewerUser: '孙八',
        statusModifyTime: '2024-01-16 16:45:00',
      },
      {
        id: 3,
        requirementNumber: 'REQ202401003',
        contryName: '广东-深圳市-南山区',
        areaName: '广东省区-深圳片区',
        stationName: '深圳南山站',
        stationNumber: 'SZ_NS_001',
        address: '深圳市南山区科技园南区',
        stationUseCaseName: '园区配送',
        vehicleModelName: 'C型无人配送车',
        count: 8,
        contact: '周九',
        contactPhone: '13800138005',
        alternateContact: '吴十',
        alternateContactPhone: '13800138006',
        createTime: '2024-01-17 11:20:00',
        status: 'REJECTED',
        statusName: '已驳回',
        reviewerUser: '郑十一',
        statusModifyTime: '2024-01-17 15:30:00',
      },
      // 异常情况：空字段
      {
        id: 4,
        requirementNumber: '',
        contryName: '',
        areaName: '',
        stationName: '',
        stationNumber: '',
        address: '',
        stationUseCaseName: '',
        vehicleModelName: '',
        count: 0,
        contact: '',
        contactPhone: '',
        alternateContact: '',
        alternateContactPhone: '',
        createTime: '',
        status: '',
        statusName: '',
        reviewerUser: '',
        statusModifyTime: '',
      },
      // 异常情况：null值
      {
        id: 5,
        requirementNumber: null,
        contryName: null,
        areaName: null,
        stationName: null,
        stationNumber: null,
        address: null,
        stationUseCaseName: null,
        vehicleModelName: null,
        count: null,
        contact: null,
        contactPhone: null,
        alternateContact: null,
        alternateContactPhone: null,
        createTime: null,
        status: null,
        statusName: null,
        reviewerUser: null,
        statusModifyTime: null,
      },
      // 异常情况：超长字段
      {
        id: 6,
        requirementNumber:
          'REQ202401004_VERY_LONG_REQUIREMENT_NUMBER_FOR_TESTING',
        contryName:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的省市区名称用于测试前端显示效果',
        areaName:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的省区片区名称用于测试前端显示效果',
        stationName:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的站点名称用于测试前端显示效果',
        stationNumber: 'VERY_LONG_STATION_NUMBER_FOR_TESTING_FRONTEND_DISPLAY',
        address:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的详细地址用于测试前端显示效果，包含各种特殊字符@#$%^&*()',
        stationUseCaseName:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的站点用途名称用于测试前端显示效果',
        vehicleModelName:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的车型名称用于测试前端显示效果',
        count: 999999,
        contact:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的联系人姓名用于测试前端显示效果',
        contactPhone: '13800138007890123456789',
        alternateContact:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的备选联系人姓名用于测试前端显示效果',
        alternateContactPhone: '13800138008901234567890',
        createTime: '2024-01-18 23:59:59',
        status: 'REVIEW',
        statusName: '审批中',
        reviewerUser:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的审核人姓名用于测试前端显示效果',
        statusModifyTime: '2024-01-18 23:59:59',
      },
      // 异常情况：特殊字符
      {
        id: 7,
        requirementNumber: 'REQ@#$%^&*()_+{}|:"<>?[]\\;\',./',
        contryName: '特殊字符省市区@#$%^&*()',
        areaName: '特殊字符省区片区@#$%^&*()',
        stationName: '特殊字符站点@#$%^&*()',
        stationNumber: 'SPECIAL_@#$%^&*()_001',
        address: '特殊字符地址@#$%^&*()_+{}|:"<>?[]\\;\',./',
        stationUseCaseName: '特殊字符用途@#$%^&*()',
        vehicleModelName: '特殊字符车型@#$%^&*()',
        count: -1, // 负数异常
        contact: '特殊字符联系人@#$%^&*()',
        contactPhone: '@#$%^&*()',
        alternateContact: '特殊字符备选联系人@#$%^&*()',
        alternateContactPhone: '@#$%^&*()',
        createTime: '无效时间格式',
        status: 'INVALID_STATUS',
        statusName: '无效状态',
        reviewerUser: '特殊字符审核人@#$%^&*()',
        statusModifyTime: '无效时间格式',
      },
      // 异常情况：数字异常
      {
        id: 8,
        requirementNumber: 'REQ202401008',
        contryName: '北京-北京市-海淀区',
        areaName: '北京省区-北京北区',
        stationName: '北京海淀站',
        stationNumber: 'BJ_HD_002',
        address: '北京市海淀区中关村大街1号',
        stationUseCaseName: 'QA测试场',
        vehicleModelName: 'D型无人配送车',
        count: 0, // 零数量异常
        contact: '测试联系人',
        contactPhone: '1380013800', // 不完整手机号
        alternateContact: null,
        alternateContactPhone: null,
        createTime: '2024-01-19 08:00:00',
        status: 'REVIEW',
        statusName: '审批中',
        reviewerUser: '测试审核人',
        statusModifyTime: '2024-01-19 08:00:00',
      },
      // 异常情况：未来时间
      {
        id: 9,
        requirementNumber: 'REQ202401009',
        contryName: '广东-广州市-天河区',
        areaName: '广东省区-广州片区',
        stationName: '广州天河站',
        stationNumber: 'GZ_TH_001',
        address: '广州市天河区珠江新城',
        stationUseCaseName: '即时配',
        vehicleModelName: 'E型无人配送车',
        count: 2,
        contact: '未来联系人',
        contactPhone: '13800138009',
        alternateContact: '未来备选联系人',
        alternateContactPhone: '13800138010',
        createTime: '2025-12-31 23:59:59', // 未来时间
        status: 'PLACED',
        statusName: '已下单',
        reviewerUser: '未来审核人',
        statusModifyTime: '2025-12-31 23:59:59', // 未来时间
      },
      // 异常情况：过去很久的时间
      {
        id: 10,
        requirementNumber: 'REQ199901001',
        contryName: '上海-上海市-徐汇区',
        areaName: '上海省区-上海西区',
        stationName: '上海徐汇站',
        stationNumber: 'SH_XH_002',
        address: '上海市徐汇区漕河泾开发区',
        stationUseCaseName: '展示演示',
        vehicleModelName: '重型配送车',
        count: 1,
        contact: '历史联系人',
        contactPhone: '13800138011',
        alternateContact: '历史备选联系人',
        alternateContactPhone: '13800138012',
        createTime: '1999-01-01 00:00:00', // 很久以前的时间
        status: 'REJECTED',
        statusName: '已驳回',
        reviewerUser: '历史审核人',
        statusModifyTime: '1999-01-01 00:00:00', // 很久以前的时间
      },
    ],
  },
};

// 接口参数类型定义
export interface VehicleDemandPageRequest {
  startTime?: string;
  endTime?: string;
  provinceId?: number;
  cityId?: number;
  countryId?: number;
  companyCode?: string;
  areaCode?: string;
  stationNumber?: string;
  vehicleModelType?: string;
  stationUseCase?: string;
  status?: string;
  pageNum: number;
  pageSize: number;
}

export interface CascaderOption {
  id?: number;
  code?: string;
  name: string;
  children?: CascaderOption[];
}

export interface StationInfo {
  stationName: string;
  stationNumber: string;
}

export interface VehicleModelInfo {
  vehicleModelType: string;
  vehicleModelName: string;
}

export interface VehicleDemandItem {
  id: number;
  requirementNumber: string;
  contryName: string;
  areaName: string;
  stationName: string;
  stationNumber: string;
  address: string;
  stationUseCaseName: string;
  vehicleModelName: string;
  count: number;
  contact: string;
  contactPhone: string;
  alternateContact?: string;
  alternateContactPhone?: string;
  createTime: string;
  status: string;
  statusName: string;
  reviewerUser: string;
  statusModifyTime: string;
}

export interface VehicleDemandPageResponse {
  pageNum: number;
  pageSize: number;
  pages: number;
  total: number;
  countMap: {
    TOTAL: number;
    REVIEW: number;
    PLACED: number;
    REJECTED: number;
  };
  list: VehicleDemandItem[];
}

class VehicleDemandManageApi {
  // （1）获取省市区cascader下拉框
  public async getProvinceCityCountryList(): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/common/get_province_city_country_list',
      method: Method.POST,
      body: {},
    };
    console.log('调用省市区下拉框接口了！！');
    return Promise.resolve(provinceCityCountryList);
    return request(requestOptions);
  }

  // （2）获取省区分区下拉框
  public async getProvinceAgencyAreaList(): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/common/get_province_agency_area_list',
      method: Method.POST,
      body: {},
    };
    console.log('调用省区分区下拉框接口了！！');
    return Promise.resolve(provinceAgencyAreaList);
    return request(requestOptions);
  }

  // （3）获取站点列表下拉框
  public async getStationList(): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_base/get_station_list',
      method: Method.POST,
      body: {},
    };
    console.log('调用站点列表下拉框接口了！！');
    return Promise.resolve(stationList);
    return request(requestOptions);
  }

  // （4）获取下单车型列表下拉框
  public async getVehicleModelList(): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/getVehicleModleList',
      method: Method.POST,
      body: {},
    };
    console.log('调用车辆型号下拉框接口了！！');
    return Promise.resolve(vehicleModelList);
    return request(requestOptions);
  }

  // （5）分页查询用车需求
  public async getVehicleDemandPage(
    params: VehicleDemandPageRequest,
  ): Promise<any> {
    console.log('调用分页查询用车需求下拉框接口了！！', params);

    // 使用mock数据进行分页处理
    const { pageNum = 1, pageSize = 10 } = params;
    const allData = vehicleDemandPage.data.list;
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = allData.slice(startIndex, endIndex);

    const mockResponse = {
      code: '0000',
      data: {
        pageNum,
        pageSize,
        pages: Math.ceil(allData.length / pageSize),
        total: allData.length,
        countMap: vehicleDemandPage.data.countMap,
        list: pageData,
      },
    };

    // 模拟网络延迟
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockResponse), 300);
    });

    // 真实接口调用（注释掉）
    // const requestOptions: RequestOptions = {
    //   path: '/k2/management/deployment/requirement/page',
    //   method: Method.POST,
    //   body: params,
    // };
    // return request(requestOptions);
  }
}

export default new VehicleDemandManageApi();
