.vehicle-demand-manage {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .tabs-container {
    margin: 20px 0;
    
    .tab-label-with-count {
      position: relative;
      display: inline-flex;
      align-items: center;
      
      .count-badge {
        position: absolute;
        top: -8px;
        right: -12px;
        background-color: #fb4c8d;
        border: 1px solid white;
        border-radius: 50%;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: white;
        font-weight: 600;
        padding: 0 4px;
        box-sizing: border-box;
        
        // 确保数字居中
        line-height: 1;
        
        // 处理较大数字的显示
        &:has-text {
          min-width: 20px;
          padding: 0 6px;
        }
      }
    }
  }

  .operate {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}

// 为CustomTabs添加数量徽章样式的扩展
.custom-tab-container {
  .custom-tab-text {
    .tab-label-with-count {
      position: relative;
      display: inline-block;
      
      .count-badge {
        position: absolute;
        top: -8px;
        right: -12px;
        background-color: #fb4c8d;
        border: 1px solid white;
        border-radius: 50%;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: white;
        font-weight: 600;
        padding: 0 4px;
        box-sizing: border-box;
        line-height: 1;
        z-index: 10;
        
        // 处理数字为0的情况
        &:empty {
          display: none;
        }
        
        // 处理较大数字
        &[data-count] {
          min-width: 20px;
          padding: 0 6px;
        }
      }
    }
  }
}
