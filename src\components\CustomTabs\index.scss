// 变量定义
$tab-height: 44px;
$tab-font-size: 14px;
$tab-font-family: PingFang SC;
$tab-font-weight: 600;
$selected-color: rgba(35, 37, 43, 1);
$unselected-color: rgba(134, 141, 159, 1);
$pseudo-element-size: 48px;
$pseudo-element-offset: -48px;
$pseudo-element-top: -3px;

// 背景渐变变量
$container-bg: linear-gradient(
    rgba(60, 110, 240, 0.05),
    rgba(60, 110, 240, 0.05)
  ),
  linear-gradient(rgba(241, 242, 244, 1), rgba(241, 242, 244, 1));
$selected-bg: linear-gradient(
  180deg,
  rgba(239, 241, 248, 1) 0%,
  rgba(255, 255, 255, 1) 100%
);

// 混合器：基础文字样式
@mixin base-text-style {
  font-size: $tab-font-size;
  font-family: $tab-font-family;
  font-weight: $tab-font-weight;
  cursor: pointer;
}

// 混合器：选中状态样式
@mixin selected-style {
  @include base-text-style;
  color: $selected-color;
  position: relative;
  background: $selected-bg;
}

// 混合器：未选中状态样式
@mixin unselected-style {
  @include base-text-style;
  color: $unselected-color;
}

// 混合器：伪元素基础样式
@mixin pseudo-element-base {
  content: '';
  display: block;
  position: absolute;
  top: $pseudo-element-top;
  width: $pseudo-element-size;
  height: $pseudo-element-size;
  z-index: 0;
  pointer-events: none;
}

// 混合器：左侧伪元素
@mixin pseudo-element-left {
  @include pseudo-element-base;
  background: url('@/assets/image/common/tab-left.png') no-repeat 0 / contain;
  left: $pseudo-element-offset;
}

// 混合器：右侧伪元素
@mixin pseudo-element-right {
  @include pseudo-element-base;
  background: url('@/assets/image/common/tab-right.png') no-repeat 0 / contain;
  right: $pseudo-element-offset;
}

.custom-tab-container {
  height: $tab-height;
  background: $container-bg;
  border: 2px solid rgba(255, 255, 255, 1);
  border-radius: 12px 12px 0 0;
  display: flex;

  .custom-tab-text {
    line-height: $tab-height;

    &.first {
      padding-left: 10px;
      margin-right: 10px;

      &.selected {
        @include selected-style;
        &::after {
          @include pseudo-element-right;
        }
      }

      &.unselected {
        @include unselected-style;
      }
    }

    &.non-first {
      margin-left: 30px;

      &.selected {
        @include selected-style;
        &::before {
          @include pseudo-element-left;
        }
        &::after {
          @include pseudo-element-right;
        }
      }

      &.unselected {
        @include unselected-style;
      }
    }
  }
}
