import React, { useState, useEffect, useRef, useMemo } from 'react';
import { message } from 'antd';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import { TableOperateBtn } from '@/components';
import CustomTabs from '@/components/CustomTabs';
import {
  searchConfig,
  tableColumns,
  tabsConfig,
  defaultHiddenColumns,
  defaultLeftFixedColumns,
  defaultRightFixedColumns,
} from './utils/constant';
import { VehicleDemandManageApi, CommonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  sortColumnsByState,
  createDefaultColumnsState,
} from '@jd/x-coreui/es/components/CommonTable/columnUtils';
import { formatDateToSecond } from '@/utils/utils';
import './index.scss';

const VehicleDemandManage = () => {
  const fetchApi = VehicleDemandManageApi;
  const searchFormRef = useRef<any>(null);
  const searchFormDomRef = useRef<any>(null);

  // 初始搜索条件 - 平铺结构
  const initSearchCondition = {
    provinceCityCountry: null,
    provinceAgencyArea: null,
    stationNumber: null,
    vehicleModelType: null,
    stationUseCase: null,
    createTime: null,
    startTime: null,
    endTime: null,
    provinceId: null,
    cityId: null,
    countryId: null,
    companyCode: null,
    areaCode: null,
    status: null as string | null,
    pageNum: 1,
    pageSize: 10,
  };

  const [searchCondition, setSearchCondition] = useState(initSearchCondition);
  const [activeTabKey, setActiveTabKey] = useState<string>('');
  const [countMap, setCountMap] = useState({
    TOTAL: 0,
    REVIEW: 0,
    PLACED: 0,
    REJECTED: 0,
  });
  const [selectInfo, setSelectInfo] = useState<{
    selectedRowKeys: any[];
    selectedRows: any[];
    clearFunc: any;
  }>({
    selectedRowKeys: [],
    selectedRows: [],
    clearFunc: () => {},
  });
  const [searchFormConfig, setSearchFormConfig] = useState(searchConfig);
  const defaultColumnsState = useMemo(() => {
    return createDefaultColumnsState(
      tableColumns,
      defaultHiddenColumns,
      defaultLeftFixedColumns,
      defaultRightFixedColumns,
    );
  }, []);
  const { columnsState, setColumnsState } = useState<any>(defaultColumnsState);

  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    fetchApi.getVehicleDemandPage,
  );

  // 页面初始化
  useEffect(() => {
    // 初始化下拉框数据
    initializeDropdownData();
    // 初始化时获取数据
    handleSearch(initSearchCondition);
  }, []);

  // 初始化下拉框数据
  const initializeDropdownData = async () => {
    try {
      const [
        provinceCityRes,
        provinceAgencyRes,
        stationRes,
        vehicleModelRes,
        stationUseCaseRes,
      ] = await Promise.all([
        fetchApi.getProvinceCityCountryList(),
        fetchApi.getProvinceAgencyAreaList(),
        fetchApi.getStationList(),
        fetchApi.getVehicleModelList(),
        new CommonApi().getCommonDropDown({ keyList: ['STATION_USE_CASE'] }),
      ]);

      // 更新搜索表单配置
      setSearchFormConfig({
        ...searchConfig,
        fields: searchConfig.fields.map((field) => {
          if (field.fieldName === 'provinceCityCountry') {
            return {
              ...field,
              options:
                provinceCityRes.code === '0000' ? provinceCityRes.data : [],
            };
          }
          if (field.fieldName === 'provinceAgencyArea') {
            return {
              ...field,
              options:
                provinceAgencyRes.code === '0000' ? provinceAgencyRes.data : [],
            };
          }
          if (field.fieldName === 'stationNumber') {
            return {
              ...field,
              options:
                stationRes.code === '0000'
                  ? stationRes.data.map((item: any) => ({
                      value: item.stationNumber,
                      label: item.stationName,
                    }))
                  : [],
            };
          }
          if (field.fieldName === 'vehicleModelType') {
            return {
              ...field,
              options:
                vehicleModelRes.code === '0000'
                  ? vehicleModelRes.data.map((item: any) => ({
                      value: item.vehicleModelType,
                      label: item.vehicleModelName,
                    }))
                  : [],
            };
          }
          if (field.fieldName === 'stationUseCase') {
            return {
              ...field,
              options:
                stationUseCaseRes.code === '0000'
                  ? stationUseCaseRes.data.stationUseCaseList?.map(
                      (item: any) => ({
                        value: item.code,
                        label: item.name,
                      }),
                    ) || []
                  : [],
            };
          }
          return field;
        }),
      });
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
      message.error('初始化下拉框数据失败');
    }
  };

  // 更新统计数据
  useEffect(() => {
    if ((tableData as any)?.countMap) {
      setCountMap((tableData as any).countMap);
    }
  }, [tableData]);

  // 处理搜索
  const handleSearch = (searchParams: any) => {
    const condition = {
      ...searchParams,
      status: activeTabKey || null,
      pageNum: 1,
    };
    setSearchCondition(condition);
  };

  // 搜索按钮点击
  const onSearchClick = (values: any) => {
    const formatValues = formatSearchValues(values);
    handleSearch(formatValues);
  };

  // 重置按钮点击
  const onResetClick = () => {
    const resetValues = {
      ...initSearchCondition,
      status: activeTabKey || null,
    };
    setSearchCondition(resetValues);
    if (searchFormRef.current) {
      searchFormRef.current.resetFields();
    }
  };

  // 格式化搜索值
  const formatSearchValues = (values: any) => {
    const formatTime: any =
      values?.createTime?.length > 0
        ? formatDateToSecond(values.createTime)
        : {};

    return {
      ...values,
      startTime: formatTime.startTime
        ? `${formatTime.startTime.split(' ')[0]} 00:00:00`
        : null,
      endTime: formatTime.endTime
        ? `${formatTime.endTime.split(' ')[0]} 23:59:59`
        : null,
      provinceId: values.provinceCityCountry?.[0] || null,
      cityId: values.provinceCityCountry?.[1] || null,
      countryId: values.provinceCityCountry?.[2] || null,
      companyCode: values.provinceAgencyArea?.[0] || null,
      areaCode: values.provinceAgencyArea?.[1] || null,
    };
  };

  // Tab切换
  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
    const currentSearchForm = searchFormRef.current?.getFieldsValue() || {};
    const formatValues = formatSearchValues(currentSearchForm);

    const newCondition = {
      ...formatValues,
      status: key || null,
      pageNum: 1,
      pageSize: searchCondition?.pageSize,
    };
    setSearchCondition(newCondition);

    // 清空选择
    selectInfo.clearFunc();
    setSelectInfo({
      selectedRowKeys: [],
      selectedRows: [],
      clearFunc: () => {},
    });
  };

  // 格式化表格列 - 应用列配置
  const formatColumns = useMemo(() => {
    return tableColumns.map((col) => {
      if (col?.dataIndex === 'operation') {
        return {
          ...col,
          render: (_: any, record: any) => {
            return (
              <div className="operate">
                <TableOperateBtn
                  title="查看详情"
                  handleClick={() => {
                    console.log('查看详情', record);
                    message.info(`查看详情: ${record.requirementNumber}`);
                  }}
                />
              </div>
            );
          },
        };
      }
      return {
        ...col,
        render: (text: any) => `${text || '-'}`,
      };
    });
  }, []);

  const dynamicColumns = useMemo(() => {
    return sortColumnsByState(formatColumns, columnsState);
  }, [columnsState]);

  // 中部按钮配置
  const middleBtns: any[] = [
    {
      show: true,
      title: '新增需求',
      key: 'addDemand',
      onClick: () => {
        console.log('新增需求');
        message.info('新增需求功能待开发');
      },
    },
    {
      show: true,
      title: '批量审批',
      key: 'batchApprove',
      onClick: () => {
        if (selectInfo.selectedRowKeys.length === 0) {
          message.error('请至少选择一条数据');
          return;
        }
        console.log('批量审批', selectInfo.selectedRowKeys);
        message.info('批量审批功能待开发');
      },
    },
    {
      show: true,
      title: '导出',
      key: 'export',
      onClick: () => {
        if (selectInfo.selectedRowKeys.length === 0) {
          message.error('请至少选择一条数据进行导出');
          return;
        }
        console.log('导出', selectInfo.selectedRowKeys);
        message.info('导出功能待开发');
      },
    },
  ];

  // 渲染带数量的Tab标签
  const renderTabsWithCount = () => {
    return tabsConfig.map((tab) => ({
      key: tab.key,
      label: (
        <div className="tab-label-with-count">
          <span>{tab.label}</span>
          <div className="count-badge">
            {countMap[tab.statusKey as keyof typeof countMap] || 0}
          </div>
        </div>
      ),
    }));
  };

  return (
    <div className="vehicle-demand-manage">
      {/* 搜索表单 */}
      <div ref={searchFormDomRef}>
        <CommonForm
          formConfig={searchFormConfig}
          defaultValue={searchCondition}
          layout="inline"
          formType="search"
          colon={false}
          getFormInstance={(ref) => (searchFormRef.current = ref)}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
        />
      </div>

      {/* 自定义Tabs */}
      <div className="tabs-container">
        <CustomTabs
          items={renderTabsWithCount()}
          activeKey={activeTabKey}
          onChange={handleTabChange}
        />
      </div>

      {/* 表格 */}
      <CommonTable
        tableListData={{
          list: (tableData as any)?.list ?? [],
          totalNumber: (tableData as any)?.total,
          totalPage: (tableData as any)?.pages,
        }}
        tableKey={'vehicle-demand-manage-table'}
        columns={dynamicColumns}
        loading={loading}
        rowKey="id"
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
        crossPageSelect={(keys: any, rows: any, clearFunc: any) => {
          setSelectInfo({
            selectedRowKeys: keys,
            selectedRows: rows,
            clearFunc: clearFunc,
          });
        }}
        searchRef={searchFormDomRef}
        // 列配置相关属性
        showColumnSetting={true}
        columnsState={{
          value: columnsState,
          onChange: setColumnsState,
          persistenceType: 'localStorage',
        }}
        defaultColumnsState={defaultColumnsState}
      />
    </div>
  );
};

export default React.memo(VehicleDemandManage);
